name = "shipany-template-one"
main = ".open-next/worker.js"
compatibility_date = "2025-03-01"
compatibility_flags = ["nodejs_compat", "global_fetch_strictly_public"]

[assets]
binding = "ASSETS"
directory = ".open-next/assets"

[observability]
enabled = true

[vars]
# Web Information
NEXT_PUBLIC_WEB_URL = "https://template.picadabra.ai"
NEXT_PUBLIC_PROJECT_NAME = "Appsolve"

# Database with Supabase
DATABASE_URL = "postgresql://postgres.ptioywqtrakifmfznzvw:<EMAIL>:5432/postgres"

# Auth with next-auth
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="
AUTH_URL = "https://template.picadabra.ai/api/auth"
AUTH_TRUST_HOST = true

# Google Auth
AUTH_GOOGLE_ID = "342741658547-nmjqm75nhkoq1cdk4smj2hi7jse9sc9c.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "342741658547-nmjqm75nhkoq1cdk4smj2hi7jse9sc9c.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# Github Auth
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# Analytics with Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# Analytics with OpenPanel
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# Analytics with Plausible
NEXT_PUBLIC_PLAUSIBLE_DOMAIN = ""
NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL = ""

# Payment with Stripe
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = ""

NEXT_PUBLIC_DEFAULT_THEME = "light"

# Storage with aws s3 sdk
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

# Google Adsence Code
NEXT_PUBLIC_GOOGLE_ADCODE = ""